
export interface SubCategory {
  id: string;
  name: string;
  allocatedAmount: number;
}

export interface Category {
  id: string;
  name: string;
  budget: number;
  subCategories: SubCategory[];
  isVisible?: boolean; // Added for individual category visibility
}

export interface FinancialGoal {
  id: string;
  name: string;
  targetAmount: number;
  savedAmount: number;
  icon?: string; // e.g., 'Vacation', 'Gadget'
  dateSet: string;
  dateAchieved?: string | null;
}

export interface BudgetData {
  totalIncome: number;
  categories: Category[];
  financialGoal: FinancialGoal | null;
  achievements: string[]; // Array of achievement IDs
  balancesVisible: boolean;
}

// Income Management Types
export interface IncomeSource {
  id: string;
  name: string;
  amount: number;
  frequency: 'weekly' | 'monthly' | 'yearly' | 'one-time';
  category: string;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
}

// Analytics Types
export interface SpendingTrend {
  month: string;
  amount: number;
  category: string;
}

export interface BudgetPerformance {
  categoryId: string;
  categoryName: string;
  budgeted: number;
  spent: number;
  remaining: number;
  percentage: number;
}

// Goals Types
export interface Goal {
  id: string;
  name: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'savings' | 'debt' | 'investment' | 'purchase' | 'other';
  priority: 'low' | 'medium' | 'high';
  isCompleted: boolean;
  createdAt: string;
  completedAt?: string;
}

// Achievement Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'savings' | 'budgeting' | 'goals' | 'consistency' | 'milestone';
  isUnlocked: boolean;
  unlockedAt?: string;
  progress: number; // 0-100
  requirement: string;
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  category: 'budget' | 'goal' | 'achievement' | 'system';
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
}

// Settings Types
export interface UserSettings {
  currency: string;
  dateFormat: string;
  notifications: {
    budgetAlerts: boolean;
    goalReminders: boolean;
    achievements: boolean;
    weeklyReports: boolean;
  };
  privacy: {
    showBalances: boolean;
    dataSharing: boolean;
  };
  theme: 'light' | 'dark' | 'system';
}

