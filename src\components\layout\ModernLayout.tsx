'use client';

import { useState, ReactNode } from 'react';
import { motion } from 'framer-motion';
import ModernSidebar from './ModernSidebar';
import ModernTopbar from './ModernTopbar';

interface ModernLayoutProps {
  children: ReactNode;
  balancesVisible?: boolean;
  onToggleBalances?: () => void;
}

export default function ModernLayout({ 
  children, 
  balancesVisible = true, 
  onToggleBalances 
}: ModernLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  return (
    <div className="min-h-screen bg-background">
      <ModernSidebar 
        isOpen={sidebarOpen} 
        onToggle={toggleSidebar} 
      />
      
      <div className="lg:ml-80">
        <ModernTopbar 
          onMenuClick={toggleSidebar}
          balancesVisible={balancesVisible}
          onToggleBalances={onToggleBalances}
        />
        
        <motion.main
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="flex-1 p-4 lg:p-6"
        >
          {children}
        </motion.main>
      </div>
    </div>
  );
}
