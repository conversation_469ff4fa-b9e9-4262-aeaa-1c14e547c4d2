'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  Wallet, 
  FolderOpen, 
  BarChart3, 
  Target, 
  Trophy, 
  FileText, 
  Bell, 
  Settings, 
  HelpCircle,
  LogOut,
  X,
  PiggyBank
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface ModernSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const sidebarItems = [
  {
    icon: Home,
    label: 'Dashboard',
    href: '/'
  },
  {
    icon: Wallet,
    label: 'Income',
    href: '/income'
  },
  {
    icon: FolderOpen,
    label: 'Categories',
    href: '/categories'
  },
  {
    icon: BarChart3,
    label: 'Analytics',
    href: '/analytics'
  },
  {
    icon: Target,
    label: 'Goals',
    href: '/goals'
  },
  {
    icon: Trophy,
    label: 'Achievements',
    href: '/achievements'
  },
  {
    icon: FileText,
    label: 'Reports',
    href: '/reports'
  },
  {
    icon: Bell,
    label: 'Notifications',
    href: '/notifications'
  },
  {
    icon: Settings,
    label: 'Settings',
    href: '/settings'
  },
  {
    icon: HelpCircle,
    label: 'Help',
    href: '/help'
  }
];

export default function ModernSidebar({ isOpen, onToggle }: ModernSidebarProps) {
  const pathname = usePathname();
  const { currentUser, signOut } = useAuth();
  const { toast } = useToast();

  const getInitials = (email?: string | null, name?: string | null) => {
    if (name) {
      const nameParts = name.split(' ').filter(Boolean);
      if (nameParts.length > 1) {
        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
      }
      return nameParts[0][0].toUpperCase();
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return 'U';
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Signed out successfully",
        description: "You have been logged out of your account.",
      });
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "There was a problem signing you out. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            onClick={onToggle}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          x: isOpen ? 0 : -320,
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
        className="fixed left-0 top-0 z-50 h-full w-80 bg-card/95 backdrop-blur-xl border-r border-border/50 lg:translate-x-0 lg:static lg:z-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border/50">
          <Link href="/" className="flex items-center gap-3" onClick={onToggle}>
            <div className="p-2 rounded-xl bg-primary/10 border border-primary/20">
              <PiggyBank className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">BudgetWise</h1>
              <p className="text-xs text-muted-foreground">Smart Financial Management</p>
            </div>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="lg:hidden h-8 w-8 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link key={item.href} href={item.href} onClick={onToggle}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`
                    flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200
                    ${isActive 
                      ? 'bg-primary/10 text-primary border border-primary/20 shadow-lg shadow-primary/10' 
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                    }
                  `}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <span className="font-medium">{item.label}</span>
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="ml-auto w-2 h-2 rounded-full bg-primary"
                    />
                  )}
                </motion.div>
              </Link>
            );
          })}
        </nav>

        {/* User Profile */}
        <div className="p-4 border-t border-border/50">
          {currentUser ? (
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 rounded-xl bg-muted/30">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={currentUser.photoURL || undefined} />
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {getInitials(currentUser.email, currentUser.displayName)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">
                    {currentUser.displayName || currentUser.email?.split('@')[0]}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {currentUser.email}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="w-full justify-start gap-2 text-muted-foreground hover:text-foreground"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <Link href="/login">
                <Button variant="default" size="sm" className="w-full">
                  Sign In
                </Button>
              </Link>
              <Link href="/register">
                <Button variant="outline" size="sm" className="w-full">
                  Sign Up
                </Button>
              </Link>
            </div>
          )}
        </div>
      </motion.aside>
    </>
  );
}
