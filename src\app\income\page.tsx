'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Wallet, 
  Plus, 
  Edit, 
  Trash2, 
  DollarSign, 
  Calendar, 
  TrendingUp,
  MoreVertical,
  Eye,
  EyeOff
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import ModernLayout from '@/components/layout/ModernLayout';
import type { IncomeSource } from '@/lib/types';

// Sample income sources data
const sampleIncomeSources: IncomeSource[] = [
  {
    id: '1',
    name: 'Primary Salary',
    amount: 5000,
    frequency: 'monthly',
    category: 'Employment',
    isActive: true,
    startDate: '2024-01-01'
  },
  {
    id: '2',
    name: 'Freelance Work',
    amount: 800,
    frequency: 'monthly',
    category: 'Freelance',
    isActive: true,
    startDate: '2024-02-01'
  },
  {
    id: '3',
    name: 'Investment Returns',
    amount: 200,
    frequency: 'monthly',
    category: 'Investment',
    isActive: true,
    startDate: '2024-01-15'
  },
  {
    id: '4',
    name: 'Side Business',
    amount: 1200,
    frequency: 'monthly',
    category: 'Business',
    isActive: false,
    startDate: '2023-12-01',
    endDate: '2024-01-31'
  }
];

const frequencyOptions = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
  { value: 'one-time', label: 'One-time' }
];

const categoryOptions = [
  'Employment',
  'Freelance',
  'Business',
  'Investment',
  'Rental',
  'Other'
];

export default function IncomePage() {
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>(sampleIncomeSources);
  const [balancesVisible, setBalancesVisible] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingSource, setEditingSource] = useState<IncomeSource | null>(null);
  const [newSource, setNewSource] = useState({
    name: '',
    amount: '',
    frequency: 'monthly' as IncomeSource['frequency'],
    category: 'Employment',
    startDate: ''
  });

  const toggleBalances = () => setBalancesVisible(!balancesVisible);

  const formatCurrency = (value: number) => {
    if (!balancesVisible) return '••••';
    return `R ${value.toLocaleString()}`;
  };

  const calculateMonthlyEquivalent = (amount: number, frequency: IncomeSource['frequency']) => {
    switch (frequency) {
      case 'weekly': return amount * 4.33;
      case 'monthly': return amount;
      case 'yearly': return amount / 12;
      case 'one-time': return 0;
      default: return amount;
    }
  };

  const getTotalMonthlyIncome = () => {
    return incomeSources
      .filter(source => source.isActive)
      .reduce((total, source) => total + calculateMonthlyEquivalent(source.amount, source.frequency), 0);
  };

  const handleAddSource = () => {
    if (!newSource.name || !newSource.amount) return;

    const source: IncomeSource = {
      id: Date.now().toString(),
      name: newSource.name,
      amount: parseFloat(newSource.amount),
      frequency: newSource.frequency,
      category: newSource.category,
      isActive: true,
      startDate: newSource.startDate || new Date().toISOString().split('T')[0]
    };

    setIncomeSources([...incomeSources, source]);
    setNewSource({
      name: '',
      amount: '',
      frequency: 'monthly',
      category: 'Employment',
      startDate: ''
    });
    setIsAddDialogOpen(false);
  };

  const handleEditSource = (source: IncomeSource) => {
    setEditingSource(source);
    setNewSource({
      name: source.name,
      amount: source.amount.toString(),
      frequency: source.frequency,
      category: source.category,
      startDate: source.startDate || ''
    });
    setIsAddDialogOpen(true);
  };

  const handleUpdateSource = () => {
    if (!editingSource || !newSource.name || !newSource.amount) return;

    const updatedSource: IncomeSource = {
      ...editingSource,
      name: newSource.name,
      amount: parseFloat(newSource.amount),
      frequency: newSource.frequency,
      category: newSource.category,
      startDate: newSource.startDate || editingSource.startDate
    };

    setIncomeSources(incomeSources.map(source => 
      source.id === editingSource.id ? updatedSource : source
    ));
    
    setEditingSource(null);
    setNewSource({
      name: '',
      amount: '',
      frequency: 'monthly',
      category: 'Employment',
      startDate: ''
    });
    setIsAddDialogOpen(false);
  };

  const handleDeleteSource = (id: string) => {
    setIncomeSources(incomeSources.filter(source => source.id !== id));
  };

  const toggleSourceActive = (id: string) => {
    setIncomeSources(incomeSources.map(source => 
      source.id === id ? { ...source, isActive: !source.isActive } : source
    ));
  };

  const activeIncomeSources = incomeSources.filter(source => source.isActive);
  const inactiveIncomeSources = incomeSources.filter(source => !source.isActive);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <ModernLayout balancesVisible={balancesVisible} onToggleBalances={toggleBalances}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Income Sources</h1>
            <p className="text-muted-foreground">Manage and track all your income streams</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add Income Source
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingSource ? 'Edit Income Source' : 'Add New Income Source'}
                </DialogTitle>
                <DialogDescription>
                  {editingSource ? 'Update your income source details' : 'Add a new source of income to track'}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Source Name</Label>
                  <Input
                    id="name"
                    value={newSource.name}
                    onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}
                    placeholder="e.g., Primary Salary"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={newSource.amount}
                      onChange={(e) => setNewSource({ ...newSource, amount: e.target.value })}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="frequency">Frequency</Label>
                    <Select value={newSource.frequency} onValueChange={(value: IncomeSource['frequency']) => setNewSource({ ...newSource, frequency: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {frequencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select value={newSource.category} onValueChange={(value) => setNewSource({ ...newSource, category: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={newSource.startDate}
                      onChange={(e) => setNewSource({ ...newSource, startDate: e.target.value })}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false);
                  setEditingSource(null);
                  setNewSource({
                    name: '',
                    amount: '',
                    frequency: 'monthly',
                    category: 'Employment',
                    startDate: ''
                  });
                }}>
                  Cancel
                </Button>
                <Button onClick={editingSource ? handleUpdateSource : handleAddSource}>
                  {editingSource ? 'Update' : 'Add'} Source
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Income Overview */}
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Monthly Income</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(getTotalMonthlyIncome())}</div>
              <p className="text-xs text-muted-foreground">
                From {activeIncomeSources.length} active sources
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Sources</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeIncomeSources.length}</div>
              <p className="text-xs text-muted-foreground">
                {inactiveIncomeSources.length} inactive
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Source</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(Math.max(...activeIncomeSources.map(s => calculateMonthlyEquivalent(s.amount, s.frequency))))}
              </div>
              <p className="text-xs text-muted-foreground">
                Monthly equivalent
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Active Income Sources */}
        <motion.div variants={itemVariants} className="space-y-4">
          <h2 className="text-xl font-semibold text-foreground">Active Income Sources</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {activeIncomeSources.map((source, index) => (
              <motion.div
                key={source.id}
                variants={itemVariants}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-colors">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <div>
                      <CardTitle className="text-lg">{source.name}</CardTitle>
                      <CardDescription>{source.category}</CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditSource(source)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleSourceActive(source.id)}>
                          <EyeOff className="h-4 w-4 mr-2" />
                          Deactivate
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteSource(source.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold">{formatCurrency(source.amount)}</span>
                      <Badge variant="outline">{source.frequency}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Monthly equivalent:</span>
                      <span className="font-medium">{formatCurrency(calculateMonthlyEquivalent(source.amount, source.frequency))}</span>
                    </div>
                    {source.startDate && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>Since {new Date(source.startDate).toLocaleDateString()}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Inactive Income Sources */}
        {inactiveIncomeSources.length > 0 && (
          <motion.div variants={itemVariants} className="space-y-4">
            <h2 className="text-xl font-semibold text-muted-foreground">Inactive Income Sources</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {inactiveIncomeSources.map((source, index) => (
                <motion.div
                  key={source.id}
                  variants={itemVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-card/30 backdrop-blur-sm border-border/30 opacity-60">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                      <div>
                        <CardTitle className="text-lg text-muted-foreground">{source.name}</CardTitle>
                        <CardDescription>{source.category}</CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => toggleSourceActive(source.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Reactivate
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditSource(source)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteSource(source.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-muted-foreground">{formatCurrency(source.amount)}</span>
                        <Badge variant="secondary">{source.frequency}</Badge>
                      </div>
                      {source.endDate && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span>Ended {new Date(source.endDate).toLocaleDateString()}</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </motion.div>
    </ModernLayout>
  );
}
