module.exports = {

"[project]/src/app/page.tsx [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/app/page.tsx'

failed to convert rope into string

Caused by:
- invalid utf-8 sequence of 1 bytes from index 0`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),

};