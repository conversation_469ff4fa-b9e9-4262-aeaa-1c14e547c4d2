'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FolderOpen, 
  Plus, 
  Edit, 
  Trash2, 
  DollarSign, 
  TrendingUp,
  MoreVertical,
  Eye,
  EyeOff,
  <PERSON>Chart
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ModernLayout from '@/components/layout/ModernLayout';
import type { Category, SubCategory } from '@/lib/types';

// Sample categories data
const sampleCategories: Category[] = [
  {
    id: '1',
    name: 'Groceries',
    budget: 1500,
    subCategories: [
      { id: '1-1', name: 'Fresh Produce', allocatedAmount: 500 },
      { id: '1-2', name: 'Pantry Items', allocatedAmount: 400 },
      { id: '1-3', name: 'Meat & Dairy', allocatedAmount: 600 }
    ],
    isVisible: true
  },
  {
    id: '2',
    name: 'Transportation',
    budget: 1000,
    subCategories: [
      { id: '2-1', name: 'Fuel', allocatedAmount: 600 },
      { id: '2-2', name: 'Maintenance', allocatedAmount: 300 },
      { id: '2-3', name: 'Insurance', allocatedAmount: 100 }
    ],
    isVisible: true
  },
  {
    id: '3',
    name: 'Entertainment',
    budget: 500,
    subCategories: [
      { id: '3-1', name: 'Movies & Shows', allocatedAmount: 200 },
      { id: '3-2', name: 'Dining Out', allocatedAmount: 300 }
    ],
    isVisible: true
  },
  {
    id: '4',
    name: 'Utilities',
    budget: 400,
    subCategories: [
      { id: '4-1', name: 'Electricity', allocatedAmount: 200 },
      { id: '4-2', name: 'Water', allocatedAmount: 100 },
      { id: '4-3', name: 'Internet', allocatedAmount: 100 }
    ],
    isVisible: true
  }
];

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>(sampleCategories);
  const [balancesVisible, setBalancesVisible] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    budget: ''
  });

  const toggleBalances = () => setBalancesVisible(!balancesVisible);

  const formatCurrency = (value: number) => {
    if (!balancesVisible) return '••••';
    return `R ${value.toLocaleString()}`;
  };

  const getTotalBudget = () => {
    return categories.reduce((total, category) => total + category.budget, 0);
  };

  const getTotalAllocated = () => {
    return categories.reduce((total, category) => 
      total + category.subCategories.reduce((subTotal, sub) => subTotal + sub.allocatedAmount, 0), 0
    );
  };

  const getCategoryUtilization = (category: Category) => {
    const allocated = category.subCategories.reduce((total, sub) => total + sub.allocatedAmount, 0);
    return category.budget > 0 ? (allocated / category.budget) * 100 : 0;
  };

  const handleAddCategory = () => {
    if (!newCategory.name || !newCategory.budget) return;

    const category: Category = {
      id: Date.now().toString(),
      name: newCategory.name,
      budget: parseFloat(newCategory.budget),
      subCategories: [],
      isVisible: true
    };

    setCategories([...categories, category]);
    setNewCategory({ name: '', budget: '' });
    setIsAddDialogOpen(false);
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setNewCategory({
      name: category.name,
      budget: category.budget.toString()
    });
    setIsAddDialogOpen(true);
  };

  const handleUpdateCategory = () => {
    if (!editingCategory || !newCategory.name || !newCategory.budget) return;

    const updatedCategory: Category = {
      ...editingCategory,
      name: newCategory.name,
      budget: parseFloat(newCategory.budget)
    };

    setCategories(categories.map(category => 
      category.id === editingCategory.id ? updatedCategory : category
    ));
    
    setEditingCategory(null);
    setNewCategory({ name: '', budget: '' });
    setIsAddDialogOpen(false);
  };

  const handleDeleteCategory = (id: string) => {
    setCategories(categories.filter(category => category.id !== id));
  };

  const toggleCategoryVisibility = (id: string) => {
    setCategories(categories.map(category => 
      category.id === id ? { ...category, isVisible: !category.isVisible } : category
    ));
  };

  const visibleCategories = categories.filter(category => category.isVisible);
  const hiddenCategories = categories.filter(category => !category.isVisible);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <ModernLayout balancesVisible={balancesVisible} onToggleBalances={toggleBalances}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Budget Categories</h1>
            <p className="text-muted-foreground">Organize and manage your spending categories</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </DialogTitle>
                <DialogDescription>
                  {editingCategory ? 'Update your category details' : 'Create a new budget category'}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Category Name</Label>
                  <Input
                    id="name"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                    placeholder="e.g., Groceries"
                  />
                </div>
                <div>
                  <Label htmlFor="budget">Budget Amount</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={newCategory.budget}
                    onChange={(e) => setNewCategory({ ...newCategory, budget: e.target.value })}
                    placeholder="0"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false);
                  setEditingCategory(null);
                  setNewCategory({ name: '', budget: '' });
                }}>
                  Cancel
                </Button>
                <Button onClick={editingCategory ? handleUpdateCategory : handleAddCategory}>
                  {editingCategory ? 'Update' : 'Add'} Category
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Categories Overview */}
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(getTotalBudget())}</div>
              <p className="text-xs text-muted-foreground">
                Across {categories.length} categories
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Allocated</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(getTotalAllocated())}</div>
              <p className="text-xs text-muted-foreground">
                {((getTotalAllocated() / getTotalBudget()) * 100).toFixed(1)}% of budget
              </p>
            </CardContent>
          </Card>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Remaining Budget</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(getTotalBudget() - getTotalAllocated())}</div>
              <p className="text-xs text-muted-foreground">
                Available to allocate
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Active Categories */}
        <motion.div variants={itemVariants} className="space-y-4">
          <h2 className="text-xl font-semibold text-foreground">Active Categories</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {visibleCategories.map((category, index) => {
              const utilization = getCategoryUtilization(category);
              const allocated = category.subCategories.reduce((total, sub) => total + sub.allocatedAmount, 0);
              
              return (
                <motion.div
                  key={category.id}
                  variants={itemVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-colors">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                      <div>
                        <CardTitle className="text-lg">{category.name}</CardTitle>
                        <CardDescription>
                          {category.subCategories.length} subcategories
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditCategory(category)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => toggleCategoryVisibility(category.id)}>
                            <EyeOff className="h-4 w-4 mr-2" />
                            Hide
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteCategory(category.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold">{formatCurrency(category.budget)}</span>
                        <Badge variant={utilization > 100 ? "destructive" : utilization > 80 ? "secondary" : "default"}>
                          {utilization.toFixed(1)}%
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Allocated</span>
                          <span className="font-medium">{formatCurrency(allocated)}</span>
                        </div>
                        <Progress value={utilization} className="h-2" />
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Remaining: {formatCurrency(category.budget - allocated)}</span>
                        </div>
                      </div>

                      {category.subCategories.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-muted-foreground">Subcategories</h4>
                          <div className="space-y-1">
                            {category.subCategories.map((sub) => (
                              <div key={sub.id} className="flex justify-between text-sm">
                                <span>{sub.name}</span>
                                <span className="font-medium">{formatCurrency(sub.allocatedAmount)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </motion.div>
    </ModernLayout>
  );
}
