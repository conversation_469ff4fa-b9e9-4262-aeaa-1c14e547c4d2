{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "e59c2e3be8995739428ed1c59d61a904", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "774ea7a55216d92206157c16176d94874d41504c89c333f893ee200a184703ec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "091f462fc29e9b6877b69816dc16e56ef7ab72d629449d1b1541db975e0f3edb"}}}, "sortedMiddleware": ["/"], "functions": {}}